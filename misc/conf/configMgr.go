package conf

import (
	"context"
	"fmt"
	"os"
	"path/filepath"
	"reflect"
	"strings"
	"sync"
	"time"

	"bitbucket.org/kingsgroup/gog-knights/minisrv/system"
	"bitbucket.org/kingsgroup/gog-knights/misc"
	"gitlab-ee.funplus.io/backend-platform/zplus-go/logging"
	"gitlab-ee.funplus.io/backend-platform/zplus-go/tasklet"
	"gitlab-ee.funplus.io/backend-platform/zplus-go/types"
)

var configMgr *ConfigMgr

func GConfigMgr() *ConfigMgr {
	return configMgr
}

func SetUpConfigMgr() {
	configMgr = &ConfigMgr{
		fileMd5:     make(map[string]string),
		collections: make([]CollectionInfo, 0),
	}
}

type reloadState int

const (
	Ready reloadState = iota
	Reloading
)

type ConfigType int

type configContextKey struct{}

const (
	VersionFile = "Version.json"
)

var fileOnload = make(map[string]func(collections interface{}, config interface{}))

func RegisterOnload(filename string, onload func(collections interface{}, config interface{})) {
	fileOnload[filename] = onload
}

type CollectionInfo struct {
	dir        string
	collection interface{} // *config collection
}

type configCtx struct {
	context.Context
	collections []CollectionInfo
}

func (cc configCtx) Value(key interface{}) interface{} {
	if key == (configContextKey{}) {
		return true
	}

	if t, ok := key.(ConfigType); ok {
		if int(t) < len(cc.collections) {
			return cc.collections[t].collection
		}

		return nil
	}

	return cc.Context.Value(key)
}

type ConfigMgr struct {
	sync.RWMutex
	gameConfDir    string
	version        string
	loadTime       time.Time
	hotReloadTypes []ConfigType
	state          reloadState
	collections    []CollectionInfo // config type key => *config collection
	fileMd5        map[string]string
}

func (cm *ConfigMgr) SetupGameConfig(confDir string) {
	cm.gameConfDir = confDir
	cm.state = Ready
}

func (cm *ConfigMgr) SetHotReloadConfigType(t ConfigType) {
	cm.hotReloadTypes = append(cm.hotReloadTypes, t)
}

func (cm *ConfigMgr) RegisterConfigCollection(confDir string, collection interface{}) ConfigType {
	cm.Lock()
	defer cm.Unlock()

	for i := range cm.collections {
		sc := &cm.collections[i]
		if sc.collection == collection {
			panic(fmt.Errorf("register duplicate config, configTypeKey: %d", i))
		}
	}

	l := len(cm.collections)
	cm.collections = append(cm.collections, CollectionInfo{
		dir:        confDir,
		collection: collection,
	})

	return ConfigType(l)
}
func (cm *ConfigMgr) GetCurrVersion() string {
	cm.RLock()
	defer cm.RUnlock()
	return cm.version
}

type ConfigInfo struct {
	Version         string
	ReloadTimeStamp int64
}

func (cm *ConfigMgr) GetCurrentConfigInfo() *ConfigInfo {
	cm.RLock()
	defer cm.RUnlock()
	return &ConfigInfo{
		Version:         cm.version,
		ReloadTimeStamp: cm.loadTime.Unix(),
	}
}

func (cm *ConfigMgr) ContextWithLatestConfig(ctx context.Context) context.Context {
	cm.RLock()
	defer cm.RUnlock()
	return &configCtx{
		ctx,
		cm.collections,
	}
}

func (cm *ConfigMgr) GetConfig(configTypeKey ConfigType) interface{} {
	cm.RLock()
	defer cm.RUnlock()
	return cm.collections[configTypeKey].collection
}

func (cm *ConfigMgr) StartUpLoadConfig(t ConfigType, versionMark bool) error {
	return cm.loadNewConfig(t, versionMark, false)
}

func (cm *ConfigMgr) HotReloadConfig(resetTopic bool) error {
	for _, t := range cm.hotReloadTypes {
		err := cm.loadNewConfig(t, true, resetTopic)
		if err != nil {
			return err
		}
	}
	if !resetTopic {
		msg := fmt.Sprintf("hot reload config success new conf version:  %s", cm.version)
		notifyHotFixMsg(msg)
	}
	return nil
}

func (cm *ConfigMgr) loadNewConfig(t ConfigType, versionMark, resetTopic bool) (err error) {
	var versionInFile string
	var origFileMd5 map[string]string
	var origCollection CollectionInfo
	check := func() error {
		cm.RLock()
		defer cm.RUnlock()
		if versionMark && !resetTopic {
			versionInFile, err = cm.getVersionInFile()
			if err != nil {
				return err
			}
			if versionInFile == cm.version {
				return fmt.Errorf("version not changed, no need to reload, current version: %s", cm.version)
			}
		}
		if cm.state == Reloading {
			return fmt.Errorf("reloading already triggered")
		}
		cm.state = Reloading
		origFileMd5 = cm.fileMd5
		origCollection = cm.collections[t]
		if resetTopic {
			logging.Infow(context.Background(), "resetTopic  ", "origCollection.dir", origCollection.dir, "gameConfDir", cm.gameConfDir)
			origCollection.dir = cm.gameConfDir
		}
		return nil
	}
	if err = check(); err != nil {
		return err
	}
	newInfo, fileMd5, err := cm.loadAllConfig(origFileMd5, origCollection)
	if err != nil {
		return err
	}
	cm.Lock()
	defer cm.Unlock()
	cm.collections[t] = newInfo
	for file, md5 := range fileMd5 {
		cm.fileMd5[file] = md5
	}
	cm.loadTime = time.Now()
	cm.state = Ready
	if versionInFile != "" {
		cm.version = versionInFile
	}
	return nil
}

func (cm *ConfigMgr) getVersionInFile() (string, error) {
	filePath := filepath.Join(cm.gameConfDir, "..", VersionFile)
	content, err := os.ReadFile(filePath)
	if err != nil {
		return "", err
	}
	version := strings.Trim(string(content), ` \t\n"`)
	if len(version) == 0 {
		return "", fmt.Errorf("empty version in file")
	}
	return version, nil
}

func (cm *ConfigMgr) loadAllConfig(origFileMd5 map[string]string, originCollection CollectionInfo) (CollectionInfo, map[string]string, error) {
	newFileMd5 := make(map[string]string)
	oc := originCollection.collection
	nc, err := types.CopyStruct(oc) // new一个新的config collection
	if err != nil {
		return originCollection, nil, err
	}

	val := reflect.ValueOf(nc).Elem()
	typ := val.Type()
	for i, l := 0, val.NumField(); i < l; i++ {
		fileName := typ.Field(i).Tag.Get("file")
		if fileName == "" {
			continue
		}

		vf := val.Field(i).Addr().Interface()
		newMd5, err := LoadConfFile(originCollection.dir, fileName, origFileMd5[fileName], vf)
		if err != nil {
			return originCollection, nil, err
		}
		if newMd5 == origFileMd5[fileName] { // 配置没有变化，还使用之前的配置
			continue
		}

		newFileMd5[fileName] = newMd5
		onload, ok := fileOnload[fileName]
		if ok {
			onload(nc, vf)
		}

		logging.Infow(nil, "config_reload", "file", fileName)
	}

	newCollection := CollectionInfo{
		dir:        originCollection.dir,
		collection: nc,
	}
	return newCollection, newFileMd5, nil
}

func parseFileTag(tagVal string) (fileName string, err error) {
	parts := strings.Split(tagVal, ",")
	if len(parts) == 0 {
		return "", fmt.Errorf("invalid tag: %s", tagVal)
	}
	fileName = parts[0]
	if !strings.HasSuffix(fileName, ".json") {
		return "", fmt.Errorf("invalid config fileName name: %s, %s", fileName, tagVal)
	}
	return
}

func WithConfig(ctx context.Context) context.Context {
	return configMgr.ContextWithLatestConfig(ctx)
}

func GetConfigFromContext(ctx context.Context, configTypeKey ConfigType) interface{} {
	return ctx.Value(configTypeKey)
}

func InteractionMiddleware(next tasklet.Handler) tasklet.Handler {
	return func(ctx context.Context) {
		ctx = WithConfig(ctx)
		next(ctx)
	}
}

func HotFix(version string) error {
	conf := GConfigMgr().GetCurrentConfigInfo()
	var err error
	if conf.Version == version || version == "" {
		err = fmt.Errorf("reloading already triggered")
	}
	err = GConfigMgr().HotReloadConfig(false)
	return err
}

func notifyHotFixMsg(msg string) {
	ip, err := misc.GetIP("10.*", "192.*", "172.*")
	if err != nil {
		panic(err)
	}
	misc.SendNoticeMsg(context.Background(), misc.MsgHotFixNotice, fmt.Errorf("kingdom[%d][%s] reload config ok [%s]", system.KingdomId, ip, time.Now()), msg)
}

// HotReloadAllConfigs triggers hot reload for all registered config types
func HotReloadAllConfigs() error {
	if configMgr == nil {
		return fmt.Errorf("config manager not initialized")
	}

	// Get all registered config types
	configTypes := make([]ConfigType, 0, len(configMgr.collections))
	for i := range configMgr.collections {
		configTypes = append(configTypes, ConfigType(i))
	}

	// Reload each config type
	for _, t := range configTypes {
		err := configMgr.loadNewConfig(t, true, false)
		if err != nil {
			return fmt.Errorf("failed to reload config type %d: %w", t, err)
		}
	}

	msg := fmt.Sprintf("hot reload all configs success, new conf version: %s", configMgr.version)
	logging.Infow(context.Background(), msg)
	notifyHotFixMsg(msg)

	return nil
}
