// Code generated by Cfgo. DO NOT EDIT.
package tempcfg

import (
	"time"
)

const enumDefaultValue = int32(0)

type Configs struct {
	timeFormat        string
	loc               *time.Location
	cfgoVersion       string
	cfgoCommitHash    string
	metaVersion       string
	dataVersion       string
	versionExtraInfos map[string]string
}

func NewConfigs() *Configs {
	configs := &Configs{}
	configs.timeFormat = "2006-01-02 15:04:05"
	configs.loc = time.FixedZone("DTZ", 0*3600)
	configs.cfgoVersion = "1.0.5"
	configs.cfgoCommitHash = "71e74f5"
	configs.metaVersion = "7429"
	configs.versionExtraInfos = make(map[string]string)
	return configs
}
func (c *Configs) LoadCsv(dir string, debugMode bool) error {
	return nil
}
