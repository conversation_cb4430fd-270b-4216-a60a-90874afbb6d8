package gmApi

import (
	"bitbucket.org/kingsgroup/gog-knights/misc/conf"
	"github.com/julienschmidt/httprouter"
	"net/http"
)

// HotReloadConfig handles the request to hot reload all configurations
func HotReloadConfig(w http.ResponseWriter, r *http.Request, _ httprouter.Params) {
	err := conf.HotReloadAllConfigs()
	if err != nil {
		jsonResponse(w, http.StatusInternalServerError, "Failed to reload configurations: "+err.Error(), nil)
		return
	}

	jsonResponse(w, http.StatusOK, "Configuration hot reload successful", nil)
}
