package gmApi

import (
	"bitbucket.org/kingsgroup/gog-knights/minisrv/cfg/cfg_mgr"
	"bitbucket.org/kingsgroup/gog-knights/minisrv/cfg/servercfg"
	"bitbucket.org/kingsgroup/gog-knights/minisrv/model"
	"github.com/julienschmidt/httprouter"
	"gitlab-ee.funplus.io/watcher/watcher/misc/json"
	"net/http"
)

var activityTypes = []ActivityTypeInfo{
	{Name: "每日特惠", Type: 0},
}

// GetActivities 获取活动列表
func GetActivities(w http.ResponseWriter, r *http.Request, _ httprouter.Params) {
	ctx := r.Context()

	// 从数据库或缓存中获取活动列表
	activities, err := model.GetAllActivities(ctx)
	if err != nil {
		jsonResponse(w, 500, "Failed to get activities", nil)
		return
	}

	// 转换为前端需要的格式
	var activityInfos []model.Activity
	for _, activity := range activities {
		// 获取活动名称和类型名称
		activityType := cfg_mgr.Cfg.ActivityTable.Get(activity.ActivityType)
		var name string
		var typeName string
		if activityType != nil {
			name = activityType.StringId
			typeName = getIapBoothTypeName(activityType.Type)
		} else {
			name = "未知活动"
			typeName = "未知类型"
		}

		activityInfos = append(activityInfos, model.Activity{
			ID:           activity.ID,
			Name:         name,
			StartTime:    activity.StartTime,
			EndTime:      activity.EndTime,
			Status:       activity.Status,
			ActivityType: activity.ActivityType,
			TypeName:     typeName, // 添加类型名称字段
		})
	}

	jsonResponse(w, 200, "Success", map[string]interface{}{
		"activities": activityInfos,
	})
}

type ActivityTypeInfo struct {
	ID   int32  `json:"id"`
	Name string `json:"name"`
	Type int32  `json:"type"`
}

// getIapBoothTypeName 获取IapBoothType的中文名称
func getIapBoothTypeName(boothType servercfg.IapBoothType) string {
	switch boothType {
	case servercfg.IapBoothType_DiamondShop:
		return "钻石商店"
	case servercfg.IapBoothType_DailySale:
		return "每日特惠"
	case servercfg.IapBoothType_RegularPack:
		return "常规礼包"
	case servercfg.IapBoothType_RegularBp:
		return "常规BP"
	case servercfg.IapBoothType_NoAds:
		return "免广告特权"
	case servercfg.IapBoothType_2X:
		return "加速特权"
	case servercfg.IapBoothType_MonthCard:
		return "月卡"
	case servercfg.IapBoothType_Fund:
		return "成长基金"
	case servercfg.IapBoothType_Sign:
		return "登录好礼"
	case servercfg.IapBoothType_TurnTable:
		return "英雄转盘"
	case servercfg.IapBoothType_Sign7:
		return "7日签到"
	default:
		return "未知类型"
	}
}

// GetActivityTypes 获取活动类型列表
func GetActivityTypes(w http.ResponseWriter, r *http.Request, _ httprouter.Params) {
	// 从配置中获取活动类型
	jsonResponse(w, 200, "Success", map[string]interface{}{
		"types": activityTypes,
	})
}

type AddActivityRequest struct {
	ActivityType int32  `json:"activityType"`
	StartTime    int64  `json:"startTime"`
	EndTime      int64  `json:"endTime"`
	ActivityName string `json:"activityName"`
}

// AddActivity 添加活动
func AddActivity(w http.ResponseWriter, r *http.Request, _ httprouter.Params) {
	ctx := r.Context()

	var req AddActivityRequest
	if err := json.NewDecoder(r.Body).Decode(&req); err != nil {
		jsonResponse(w, 400, "Invalid request body", nil)
		return
	}

	// 验证请求参数
	if req.StartTime >= req.EndTime {
		jsonResponse(w, 400, "Start time must be before end time", nil)
		return
	}

	// 检查活动类型是否存在

	// 创建活动
	err := model.CreateActivity(ctx, req.ActivityType, req.StartTime, req.EndTime, true)
	if err != nil {
		jsonResponse(w, 500, "Failed to create activity", nil)
		return
	}

	jsonResponse(w, 200, "Success", nil)
}

type ToggleActivityRequest struct {
	ActivityID int32 `json:"activityId"`
	Status     bool  `json:"status"`
}

// ToggleActivity 切换活动状态
func ToggleActivity(w http.ResponseWriter, r *http.Request, _ httprouter.Params) {
	ctx := r.Context()

	var req ToggleActivityRequest
	if err := json.NewDecoder(r.Body).Decode(&req); err != nil {
		jsonResponse(w, 400, "Invalid request body", nil)
		return
	}

	// 更新活动状态
	err := model.CloseActivity(ctx, req.ActivityID)
	if err != nil {
		jsonResponse(w, 500, "Failed to update activity status", nil)
		return
	}

	jsonResponse(w, 200, "Success", nil)
}
