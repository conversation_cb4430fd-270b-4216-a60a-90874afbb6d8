package gmApi

import (
	"context"
	"encoding/json"
	"errors"
	"github.com/julienschmidt/httprouter"
	"gitlab-ee.funplus.io/backend-platform/zplus-go/tasklet"
	"net/http"
)

func RegisterRoutes(router *httprouter.Router) {

	// 日志系统
	//http.HandleFunc("/gm/api/logs", GetLogs)

	router.POST("/gm/api/users", GetUserList)
	router.POST("/gm/api/users/ban", BanUser)
	router.POST("/gm/api/items", GetItemList)
	router.POST("/gm/api/items/add", AddItem)
	router.POST("/gm/api/mail/send", handleMailSend)
	router.POST("/gm/api/announcements/add", AddAnnouncement)

	// 活动管理相关API
	router.GET("/gm/api/activities", GetActivities)
	router.GET("/gm/api/activity-types", GetActivityTypes)
	router.POST("/gm/api/activities/add", AddActivity)
	router.POST("/gm/api/activities/toggle", ToggleActivity)

	// 国家限制相关API
	router.GET("/gm/api/country-restrictions", GetCountryRestrictions)
	router.POST("/gm/api/country-restrictions", SaveCountryRestrictions)
	router.POST("/gm/api/test-ip-restriction", TestIPRestriction)

	// Config management
	router.GET("/api/gm/config/reload", HotReloadConfig)
}

// handleMailSend 处理邮件发送请求
func handleMailSend(w http.ResponseWriter, r *http.Request, _ httprouter.Params) {
	if r.Method != http.MethodPost {
		http.Error(w, "Method not allowed", http.StatusMethodNotAllowed)
		return
	}

	var req struct {
		RecipientType string     `json:"recipientType"`
		Recipient     string     `json:"recipient"`
		MailType      string     `json:"mailType"`
		Title         string     `json:"title"`
		Content       string     `json:"content"`
		Items         []ItemInfo `json:"items"`
	}

	if err := json.NewDecoder(r.Body).Decode(&req); err != nil {
		jsonResponse(w, http.StatusBadRequest, "Invalid request body", nil)
		return
	}

	ctx := r.Context()
	var err error

	switch req.RecipientType {
	case "single":
		//uid, _ := strconv.ParseInt(req.Recipient, 10, 64)
		tasklet.Invoke(ctx, r.Method, func(ctx context.Context) {
			//g.ClientRtm.ClearProjectMessage(rtm.All)
			//_, err = model.SendSystemMail(ctx, uid, 1, 1, 1, time.Now().Unix()+86400)
		})

	case "multiple":
		//uids := strings.Split(req.Recipient, ",")
		//for _, uidStr := range uids {
		//uid, _ := strconv.ParseInt(strings.TrimSpace(uidStr), 10, 64)
		//if _, err = model.SendSystemMail(ctx, uid, 1, 1, 1, time.Now().Unix()+86400); err != nil {
		//	break
		//}
		//}
	case "all":
		// 实现全服邮件发送逻辑
		// TODO: 实现全服邮件发送
		err = errors.New("全服邮件功能暂未实现")
	default:
		err = errors.New("无效的收件人类型")
	}

	if err != nil {
		jsonResponse(w, http.StatusInternalServerError, "发送邮件失败: "+err.Error(), nil)
		return
	}

	jsonResponse(w, http.StatusOK, "邮件发送成功", nil)
}
