package model

import (
	"context"
	"errors"
)

// Activity 活动数据模型
type Activity struct {
	ID           int32  `json:"id"`
	ActivityType int32  `json:"activityType"`
	StartTime    int64  `json:"startTime"`
	EndTime      int64  `json:"endTime"`
	Status       bool   `json:"status"`
	Name         string `json:"name"`
	TypeName     string `json:"typeName,omitempty"` // 活动类型名称（可选）
}

// 内存中存储活动数据（实际项目中应该使用数据库）
var activities = []Activity{}

// GetAllActivities 获取所有活动
func GetAllActivities(ctx context.Context) ([]Activity, error) {
	return activities, nil
}

// CreateActivity 创建活动
func CreateActivity(ctx context.Context, activityType int32, startTime, endTime int64, status bool) error {
	// 生成新的活动ID
	var maxID int32 = 0
	for _, activity := range activities {
		if activity.ID > maxID {
			maxID = activity.ID
		}
	}

	newActivity := Activity{
		ID:           maxID + 1,
		ActivityType: activityType,
		StartTime:    startTime,
		EndTime:      endTime,
		Status:       status,
	}

	activities = append(activities, newActivity)
	return nil
}

func CloseActivity(ctx context.Context, activityID int32) error {
	deleteIndex := -1
	for i, activity := range activities {
		if activity.ID == activityID {
			deleteIndex = i
			break
		}
	}
	if deleteIndex != -1 {
		activities = append(activities[:deleteIndex], activities[deleteIndex+1:]...)
		return nil
	}

	return errors.New("activity not found")
}

// UpdateActivityStatus 更新活动状态
func UpdateActivityStatus(ctx context.Context, activityID int32, status bool) error {
	deleteIndex := -1
	for i, activity := range activities {
		if activity.ID == activityID {
			deleteIndex = i
			return nil
		}
	}
	if deleteIndex != -1 {
		activities = append(activities[:deleteIndex], activities[deleteIndex+1:]...)
		return nil
	}

	return errors.New("activity not found")
}
